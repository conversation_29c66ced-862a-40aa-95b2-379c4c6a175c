import { InvestigationConfig, InvestigationResults, UserInput, EvidenceItem, AuditLogEntry } from '../types';
export declare class BitcoinForensicsInvestigator {
    private config;
    private apiService;
    private investigationId;
    private evidenceItems;
    private auditLog;
    private userInput?;
    constructor(config?: Partial<InvestigationConfig>);
    private ensureOutputDirectory;
    private logAuditEvent;
    private createEvidenceItem;
    startInvestigation(userInput: UserInput): Promise<InvestigationResults>;
    private traceTransactions;
    getInvestigationId(): string;
    getEvidenceItems(): EvidenceItem[];
    getAuditLog(): AuditLogEntry[];
}
//# sourceMappingURL=investigator.d.ts.map