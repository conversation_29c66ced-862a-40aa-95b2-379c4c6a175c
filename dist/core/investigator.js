"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BitcoinForensicsInvestigator = void 0;
const uuid_1 = require("uuid");
const crypto_1 = __importDefault(require("crypto"));
const fs_1 = __importDefault(require("fs"));
const api_1 = require("../services/api");
const evidence_1 = require("../services/evidence");
const validation_1 = require("../utils/validation");
const logger_1 = require("../utils/logger");
const default_1 = require("../config/default");
class BitcoinForensicsInvestigator {
    constructor(config = {}) {
        this.evidenceItems = [];
        this.auditLog = [];
        this.config = { ...default_1.DEFAULT_CONFIG, ...config };
        this.apiService = new api_1.BitcoinAPIService(this.config);
        this.investigationId = (0, uuid_1.v4)();
        // Create output directory
        this.ensureOutputDirectory();
        // Initialize enhanced evidence service
        this.enhancedEvidenceService = new evidence_1.EnhancedEvidenceService(this.investigationId, this.config.outputDirectory);
        // Log initialization
        this.logAuditEvent('investigation_initialized', {
            investigationId: this.investigationId,
            config: this.config,
        });
        logger_1.logger.info(`Initialized Bitcoin Forensics Investigator with enhanced evidence tracking`, {
            investigationId: this.investigationId,
        });
    }
    ensureOutputDirectory() {
        if (!fs_1.default.existsSync(this.config.outputDirectory)) {
            fs_1.default.mkdirSync(this.config.outputDirectory, { recursive: true });
        }
    }
    logAuditEvent(action, details, userId = 'system') {
        const auditEntry = {
            timestamp: new Date().toISOString(),
            investigationId: this.investigationId,
            action,
            details,
            userId,
            ipAddress: 'localhost', // In a real system, this would be the actual IP
        };
        this.auditLog.push(auditEntry);
        (0, logger_1.logAuditEvent)(this.investigationId, action, details);
    }
    createEvidenceItem(evidenceType, description, data) {
        const evidenceId = (0, uuid_1.v4)();
        // Create hash of the evidence data for integrity
        const evidenceHash = crypto_1.default
            .createHash('sha256')
            .update(JSON.stringify(data, null, 0))
            .digest('hex');
        // Initialize chain of custody
        const chainOfCustody = [
            {
                timestamp: new Date().toISOString(),
                action: 'evidence_created',
                userId: 'system',
                description: `Evidence item created: ${description}`,
            },
        ];
        const evidenceItem = {
            evidenceId,
            investigationId: this.investigationId,
            evidenceType: evidenceType,
            description,
            data,
            timestamp: new Date().toISOString(),
            hashValue: evidenceHash,
            chainOfCustody,
        };
        this.evidenceItems.push(evidenceItem);
        (0, logger_1.logEvidenceCreated)(evidenceId, evidenceType, description);
        this.logAuditEvent('evidence_created', {
            evidenceId,
            evidenceType,
            description,
            hash: evidenceHash,
        });
        return evidenceItem;
    }
    async startInvestigation(userInput) {
        this.userInput = userInput;
        (0, logger_1.logInvestigationStart)(this.investigationId, userInput);
        this.logAuditEvent('investigation_started', {
            initialTxid: userInput.initialTxid,
            targetAddress: userInput.targetAddress,
            maxDepth: userInput.maxDepth,
            victimName: userInput.victimName,
        });
        logger_1.logger.info('🕵️  Starting Bitcoin forensic investigation', {
            investigationId: this.investigationId,
            txid: userInput.initialTxid,
            address: userInput.targetAddress,
            victimName: userInput.victimName,
        });
        try {
            // Validate inputs
            if (!(0, validation_1.validateTransactionId)(userInput.initialTxid)) {
                throw new Error(`Invalid transaction ID: ${userInput.initialTxid}`);
            }
            if (!(0, validation_1.validateBitcoinAddress)(userInput.targetAddress)) {
                throw new Error(`Invalid Bitcoin address: ${userInput.targetAddress}`);
            }
            // Perform transaction tracing
            const { transactions, addresses } = await this.traceTransactions(userInput.initialTxid, userInput.targetAddress, userInput.maxDepth);
            // Create basic results
            const basicResults = {
                transactionCount: transactions.length,
                addressCount: addresses.length,
                totalAmount: transactions.reduce((sum, tx) => sum + tx.amountBtc, 0),
            };
            // Create enhanced evidence for transactions and addresses
            transactions.forEach(tx => {
                const evidence = this.enhancedEvidenceService.createTransactionEvidence(tx);
                this.enhancedEvidenceService.updateChainOfCustody(evidence.evidenceId, 'evidence_collected', `Transaction evidence collected during investigation`, 'investigator');
            });
            addresses.forEach(addr => {
                const evidence = this.enhancedEvidenceService.createAddressEvidence(addr);
                this.enhancedEvidenceService.updateChainOfCustody(evidence.evidenceId, 'evidence_collected', `Address evidence collected during investigation`, 'investigator');
            });
            // Generate enhanced evidence package
            const evidencePackagePath = await this.enhancedEvidenceService.exportEvidencePackage();
            const evidenceStats = this.enhancedEvidenceService.getStatistics();
            const auditReport = this.enhancedEvidenceService.generateAuditReport();
            // Create investigation results structure
            const results = {
                investigationId: this.investigationId,
                timestamp: new Date().toISOString(),
                inputParameters: {
                    initialTxid: userInput.initialTxid,
                    targetAddress: userInput.targetAddress,
                    maxDepth: userInput.maxDepth,
                    victimName: userInput.victimName,
                    caseDescription: userInput.caseDescription,
                },
                basicResults,
                detailedTransactions: transactions,
                addressAnalysis: addresses,
                advancedAnalysis: {
                    transactionPatterns: {}, // Will be filled by analysis service
                    suspiciousActivity: {},
                    riskAssessment: {},
                },
                investigationSummary: {
                    status: 'completed',
                    keyMetrics: {
                        totalTransactions: transactions.length,
                        totalAmountBtc: basicResults.totalAmount,
                        uniqueAddresses: addresses.length,
                        maximumDepth: Math.max(...transactions.map(tx => tx.depth), 0),
                        riskLevel: 'UNKNOWN', // Will be determined by analysis
                    },
                    keyConcerns: [],
                    investigationQuality: {
                        qualityScore: 0,
                        qualityLevel: 'MEDIUM',
                        qualityFactors: [],
                        completenessPercentage: 0,
                    },
                    nextSteps: [],
                    timeline: [],
                },
                evidencePackage: this.evidenceItems,
                auditTrail: this.auditLog,
                enhancedEvidence: {
                    totalItems: evidenceStats.totalEvidence,
                    categories: Object.keys(evidenceStats.byCategory),
                    integrityStatus: evidenceStats.integrityStatus,
                    packagePath: evidencePackagePath,
                    auditReport,
                },
            };
            (0, logger_1.logInvestigationEnd)(this.investigationId, basicResults);
            this.logAuditEvent('investigation_completed', {
                transactionCount: transactions.length,
                addressCount: addresses.length,
                totalAmount: basicResults.totalAmount,
            });
            return results;
        }
        catch (error) {
            logger_1.logger.error('Investigation failed', {
                investigationId: this.investigationId,
                error: error.message,
            });
            this.logAuditEvent('investigation_failed', {
                error: error.message,
                stack: error.stack,
            });
            throw error;
        }
    }
    async traceTransactions(initialTxid, targetAddress, maxDepth) {
        const transactions = [];
        const addressMap = new Map();
        const visitedTxs = new Set([initialTxid]);
        const queue = [
            { txid: initialTxid, address: targetAddress, depth: 0 },
        ];
        // Add initial address to the map
        addressMap.set(targetAddress, {
            address: targetAddress,
            addressType: (0, validation_1.getBitcoinAddressType)(targetAddress),
            totalReceived: 0,
            totalSent: 0,
            balance: 0,
            transactionCount: 0,
            firstSeen: new Date().toISOString(),
        });
        while (queue.length > 0) {
            const { txid: currentTxid, address: sourceAddress, depth } = queue.shift();
            if (depth >= maxDepth) {
                logger_1.logger.info(`Reached maximum depth ${maxDepth} for address ${sourceAddress.substring(0, 8)}...`);
                continue;
            }
            logger_1.logger.info(`Processing transaction ${currentTxid} at depth ${depth}`);
            // Get transaction data
            const txResponse = await this.apiService.getTransaction(currentTxid);
            if (!txResponse.success || !txResponse.data) {
                logger_1.logger.warn(`Failed to fetch transaction ${currentTxid}: ${txResponse.error}`);
                continue;
            }
            const txData = txResponse.data;
            // Find outputs that go TO the source address
            const relevantOutputs = txData.vout
                .map((vout, index) => ({ vout, index }))
                .filter(({ vout }) => vout.scriptpubkey_address === sourceAddress);
            // For each relevant output, check if it gets spent
            for (const { vout, index } of relevantOutputs) {
                const outspendResponse = await this.apiService.getOutspend(currentTxid, index);
                if (!outspendResponse.success || !outspendResponse.data?.spent) {
                    continue;
                }
                const outspendData = outspendResponse.data;
                const spendingTxid = outspendData.txid;
                if (visitedTxs.has(spendingTxid)) {
                    continue;
                }
                visitedTxs.add(spendingTxid);
                // Get the spending transaction
                const spendingTxResponse = await this.apiService.getTransaction(spendingTxid);
                if (!spendingTxResponse.success || !spendingTxResponse.data) {
                    continue;
                }
                const spendingTxData = spendingTxResponse.data;
                // Analyze where the funds went
                for (const nextVout of spendingTxData.vout) {
                    const newAddress = nextVout.scriptpubkey_address;
                    if (!newAddress || newAddress === sourceAddress) {
                        continue;
                    }
                    const amountBtc = nextVout.value / 100000000;
                    // Create transaction info
                    const transactionInfo = {
                        txid: spendingTxid,
                        fromAddress: sourceAddress,
                        toAddress: newAddress,
                        amountBtc,
                        depth: depth + 1,
                        timestamp: new Date().toISOString(),
                        blockHeight: spendingTxData.status.block_height,
                        confirmations: spendingTxData.status.confirmed,
                        investigationId: this.investigationId,
                        fees: spendingTxData.fee / 100000000,
                        inputCount: spendingTxData.vin.length,
                        outputCount: spendingTxData.vout.length,
                    };
                    transactions.push(transactionInfo);
                    // Update address info
                    if (!addressMap.has(newAddress)) {
                        addressMap.set(newAddress, {
                            address: newAddress,
                            addressType: (0, validation_1.getBitcoinAddressType)(newAddress),
                            totalReceived: 0,
                            totalSent: 0,
                            balance: 0,
                            transactionCount: 0,
                            firstSeen: new Date().toISOString(),
                        });
                    }
                    const addressInfo = addressMap.get(newAddress);
                    addressInfo.totalReceived += amountBtc;
                    addressInfo.transactionCount++;
                    addressInfo.lastSeen = new Date().toISOString();
                    // Create evidence item
                    this.createEvidenceItem('transaction', `Transaction ${spendingTxid}: ${amountBtc.toFixed(8)} BTC from ${sourceAddress.substring(0, 8)}... to ${newAddress.substring(0, 8)}...`, {
                        transactionInfo,
                        rawTransactionData: spendingTxData,
                        discoveryContext: {
                            parentTxid: currentTxid,
                            outputIndex: index,
                            discoveryDepth: depth,
                        },
                    });
                    // Continue tracing
                    queue.push({ txid: spendingTxid, address: newAddress, depth: depth + 1 });
                }
            }
        }
        return {
            transactions,
            addresses: Array.from(addressMap.values()),
        };
    }
    getInvestigationId() {
        return this.investigationId;
    }
    getEvidenceItems() {
        return [...this.evidenceItems];
    }
    getAuditLog() {
        return [...this.auditLog];
    }
}
exports.BitcoinForensicsInvestigator = BitcoinForensicsInvestigator;
//# sourceMappingURL=investigator.js.map