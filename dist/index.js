#!/usr/bin/env node
"use strict";
/**
 * Bitcoin Forensic Investigation Tool v3.0
 *
 * A comprehensive cryptocurrency forensics tool designed specifically for Bitcoin scam victims.
 * This tool provides automated transaction tracing, AI-powered analysis, and professional
 * evidence collection capabilities in a user-friendly CLI interface.
 *
 * Features:
 * - Automated Bitcoin transaction tracing
 * - AI-powered suspicious activity detection
 * - Professional evidence collection and chain of custody
 * - Comprehensive reporting for legal proceedings
 * - User-friendly interface for non-technical users
 *
 * <AUTHOR> Forensics Team
 * @version 3.0.0
 * @license MIT
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BitcoinAPIService = exports.AnalysisService = exports.BitcoinForensicsInvestigator = exports.CLIInterface = void 0;
const interface_1 = require("./cli/interface");
Object.defineProperty(exports, "CLIInterface", { enumerable: true, get: function () { return interface_1.CLIInterface; } });
const logger_1 = require("./utils/logger");
const chalk_1 = __importDefault(require("chalk"));
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    logger_1.logger.error('Uncaught exception', { error: error.message, stack: error.stack });
    console.error(chalk_1.default.red('\n❌ An unexpected error occurred. Please check the logs for details.'));
    process.exit(1);
});
// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('Unhandled promise rejection', { reason, promise });
    console.error(chalk_1.default.red('\n❌ An unexpected error occurred. Please check the logs for details.'));
    process.exit(1);
});
// Handle SIGINT (Ctrl+C)
process.on('SIGINT', () => {
    console.log(chalk_1.default.yellow('\n\n⚠️  Investigation interrupted by user.'));
    logger_1.logger.info('Investigation interrupted by user (SIGINT)');
    process.exit(0);
});
// Handle SIGTERM
process.on('SIGTERM', () => {
    console.log(chalk_1.default.yellow('\n\n⚠️  Investigation terminated.'));
    logger_1.logger.info('Investigation terminated (SIGTERM)');
    process.exit(0);
});
/**
 * Main application entry point
 */
async function main() {
    try {
        // Set default log level
        (0, logger_1.setLogLevel)(process.env.LOG_LEVEL || 'info');
        // Log application start
        logger_1.logger.info('Bitcoin Forensic Investigation Tool started', {
            version: '3.0.0',
            nodeVersion: process.version,
            platform: process.platform,
            arch: process.arch,
        });
        // Initialize and run CLI interface
        const cli = new interface_1.CLIInterface();
        await cli.run();
    }
    catch (error) {
        logger_1.logger.error('Application error', { error: error.message, stack: error.stack });
        console.error(chalk_1.default.red('\n❌ Application Error:'));
        console.error(chalk_1.default.red(error.message));
        if (process.env.NODE_ENV === 'development') {
            console.error(chalk_1.default.gray('\nStack trace:'));
            console.error(chalk_1.default.gray(error.stack));
        }
        console.error(chalk_1.default.yellow('\n💡 If this problem persists, please:'));
        console.error(chalk_1.default.gray('   1. Check your internet connection'));
        console.error(chalk_1.default.gray('   2. Verify your input parameters'));
        console.error(chalk_1.default.gray('   3. Try again in a few minutes'));
        console.error(chalk_1.default.gray('   4. Check the logs directory for detailed error information'));
        process.exit(1);
    }
}
// Run the application
if (require.main === module) {
    main().catch((error) => {
        logger_1.logger.error('Fatal application error', { error: error.message, stack: error.stack });
        console.error(chalk_1.default.red('❌ Fatal error occurred. Check logs for details.'));
        process.exit(1);
    });
}
var investigator_1 = require("./core/investigator");
Object.defineProperty(exports, "BitcoinForensicsInvestigator", { enumerable: true, get: function () { return investigator_1.BitcoinForensicsInvestigator; } });
var analysis_1 = require("./services/analysis");
Object.defineProperty(exports, "AnalysisService", { enumerable: true, get: function () { return analysis_1.AnalysisService; } });
var api_1 = require("./services/api");
Object.defineProperty(exports, "BitcoinAPIService", { enumerable: true, get: function () { return api_1.BitcoinAPIService; } });
__exportStar(require("./types"), exports);
__exportStar(require("./utils/validation"), exports);
__exportStar(require("./utils/logger"), exports);
//# sourceMappingURL=index.js.map