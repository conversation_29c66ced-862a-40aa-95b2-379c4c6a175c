"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateBitcoinAddress = validateBitcoinAddress;
exports.getBitcoinAddressType = getBitcoinAddressType;
exports.validateTransactionId = validateTransactionId;
exports.validateDepth = validateDepth;
exports.validateBtcAmount = validateBtcAmount;
exports.sanitizeInput = sanitizeInput;
exports.validateEmail = validateEmail;
exports.validateDate = validateDate;
exports.validateConfig = validateConfig;
exports.validateUserInput = validateUserInput;
exports.formatValidationErrors = formatValidationErrors;
const default_1 = require("../config/default");
/**
 * Validates Bitcoin address format
 */
function validateBitcoinAddress(address) {
    if (!address || typeof address !== 'string') {
        return false;
    }
    // Check length constraints
    if (address.length < 26 || address.length > 62) {
        return false;
    }
    // Check against known patterns
    return Object.values(default_1.BITCOIN_ADDRESS_PATTERNS).some(pattern => pattern.test(address));
}
/**
 * Determines the type of Bitcoin address
 */
function getBitcoinAddressType(address) {
    if (default_1.BITCOIN_ADDRESS_PATTERNS.legacy.test(address)) {
        return 'legacy';
    }
    if (default_1.BITCOIN_ADDRESS_PATTERNS.segwit.test(address)) {
        return 'segwit';
    }
    if (default_1.BITCOIN_ADDRESS_PATTERNS.taproot.test(address)) {
        return 'taproot';
    }
    return 'unknown';
}
/**
 * Validates Bitcoin transaction ID format
 */
function validateTransactionId(txid) {
    if (!txid || typeof txid !== 'string') {
        return false;
    }
    return default_1.TRANSACTION_ID_PATTERN.test(txid);
}
/**
 * Validates investigation depth parameter
 */
function validateDepth(depth) {
    return Number.isInteger(depth) && depth >= 1 && depth <= 10;
}
/**
 * Validates BTC amount
 */
function validateBtcAmount(amount) {
    return typeof amount === 'number' && amount > 0 && amount <= 21000000; // Max BTC supply
}
/**
 * Sanitizes user input to prevent injection attacks
 */
function sanitizeInput(input) {
    if (typeof input !== 'string') {
        return '';
    }
    return input
        .trim()
        .replace(/[<>]/g, '') // Remove potential HTML tags
        .replace(/['"]/g, '') // Remove quotes
        .substring(0, 1000); // Limit length
}
/**
 * Validates email format (for contact info)
 */
function validateEmail(email) {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailPattern.test(email);
}
/**
 * Validates date format (ISO 8601)
 */
function validateDate(dateString) {
    const date = new Date(dateString);
    return !isNaN(date.getTime()) && dateString === date.toISOString().split('T')[0];
}
/**
 * Validates investigation configuration
 */
function validateConfig(config) {
    const errors = [];
    if (!config.apiBaseUrl || typeof config.apiBaseUrl !== 'string') {
        errors.push('Invalid API base URL');
    }
    if (!Number.isInteger(config.maxRetries) || config.maxRetries < 1 || config.maxRetries > 10) {
        errors.push('Max retries must be between 1 and 10');
    }
    if (!Number.isInteger(config.requestTimeout) || config.requestTimeout < 1000) {
        errors.push('Request timeout must be at least 1000ms');
    }
    if (!validateDepth(config.maxDepth)) {
        errors.push('Max depth must be between 1 and 10');
    }
    if (typeof config.rateLimitDelay !== 'number' || config.rateLimitDelay < 0) {
        errors.push('Rate limit delay must be a non-negative number');
    }
    return {
        valid: errors.length === 0,
        errors,
    };
}
/**
 * Validates user input for investigation
 */
function validateUserInput(input) {
    const errors = [];
    if (!input.victimName || typeof input.victimName !== 'string' || input.victimName.trim().length === 0) {
        errors.push('Victim name is required');
    }
    if (!input.initialTxid || !validateTransactionId(input.initialTxid)) {
        errors.push('Valid transaction ID is required');
    }
    if (!input.targetAddress || !validateBitcoinAddress(input.targetAddress)) {
        errors.push('Valid Bitcoin address is required');
    }
    if (!validateBtcAmount(input.scamAmount)) {
        errors.push('Valid BTC amount is required');
    }
    if (!validateDepth(input.maxDepth)) {
        errors.push('Investigation depth must be between 1 and 10');
    }
    if (input.contactInfo && !validateEmail(input.contactInfo)) {
        errors.push('Valid email address required for contact info');
    }
    if (input.incidentDate && !validateDate(input.incidentDate)) {
        errors.push('Valid date required for incident date (YYYY-MM-DD format)');
    }
    return {
        valid: errors.length === 0,
        errors,
    };
}
/**
 * Formats validation errors for display
 */
function formatValidationErrors(errors) {
    if (errors.length === 0) {
        return '';
    }
    if (errors.length === 1) {
        return `❌ ${errors[0]}`;
    }
    return `❌ Multiple validation errors:\n${errors.map(error => `   • ${error}`).join('\n')}`;
}
//# sourceMappingURL=validation.js.map