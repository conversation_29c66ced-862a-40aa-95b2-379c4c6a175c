import { InvestigationConfig, APIResponse, BlockstreamTransaction, BlockstreamOutspend } from '../types';
export declare class BitcoinAPIService {
    private client;
    private config;
    private requestCount;
    private lastRequestTime;
    constructor(config: InvestigationConfig);
    private setupInterceptors;
    private enforceRateLimit;
    getTransaction(txid: string): Promise<APIResponse<BlockstreamTransaction>>;
    getOutspend(txid: string, vout: number): Promise<APIResponse<BlockstreamOutspend>>;
    getAddressInfo(address: string): Promise<APIResponse<any>>;
    getAddressTransactions(address: string, lastSeenTxid?: string): Promise<APIResponse<any[]>>;
    private retryRequest;
    getRequestCount(): number;
    resetRequestCount(): void;
}
//# sourceMappingURL=api.d.ts.map