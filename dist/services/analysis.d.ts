import { TransactionInfo, PatternAnalysis, SuspiciousActivity, RiskAssessment } from '../types';
export declare class AnalysisService {
    /**
     * Generate AI-powered insights based on transaction patterns
     */
    generateAIInsights(transactions: TransactionInfo[], patterns: PatternAnalysis, suspiciousActivity: SuspiciousActivity): string[];
    /**
     * Analyze transaction patterns for suspicious activity detection
     */
    analyzeTransactionPatterns(transactions: TransactionInfo[]): PatternAnalysis;
    /**
     * Detect various types of suspicious activity patterns
     */
    detectSuspiciousActivity(transactions: TransactionInfo[]): SuspiciousActivity;
    /**
     * Generate comprehensive risk assessment
     */
    generateRiskAssessment(transactions: TransactionInfo[], patterns: PatternAnalysis, suspiciousActivity: SuspiciousActivity): RiskAssessment;
    private analyzeTimingPatterns;
    private analyzeAmountPatterns;
    private analyzeAddressReuse;
    private analyzeClusteringHints;
    private calculateRiskIndicators;
    private detectMixingServices;
    private detectExchangeDeposits;
    private detectPeelChains;
    private detectConsolidationPatterns;
    private detectPrivacyCoinInteractions;
    private classifyRiskLevel;
    private classifySuspicionLevel;
    private generateRiskRecommendations;
    private getEmptyPatternAnalysis;
}
//# sourceMappingURL=analysis.d.ts.map