"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalysisService = void 0;
const default_1 = require("../config/default");
const logger_1 = require("../utils/logger");
class AnalysisService {
    /**
     * Generate AI-powered insights based on transaction patterns
     */
    generateAIInsights(transactions, patterns, suspiciousActivity) {
        const insights = [];
        // Timing-based insights
        if (patterns.timingAnalysis.suspiciousTiming) {
            insights.push('🤖 AI Insight: Rapid transaction succession detected - this pattern is commonly associated with automated fund movement or mixing services.');
        }
        // Amount-based insights
        if (patterns.amountAnalysis.potentialStructuring) {
            insights.push('🤖 AI Insight: Round amount patterns suggest potential structuring to avoid detection thresholds.');
        }
        if (patterns.amountAnalysis.potentialSplitting) {
            insights.push('🤖 AI Insight: Similar transaction amounts indicate possible fund splitting strategy to obscure the money trail.');
        }
        // Mixing service insights
        if (suspiciousActivity.mixingServices.detected) {
            insights.push('🤖 AI Insight: Transaction patterns consistent with cryptocurrency mixing services - funds may be intentionally obfuscated.');
        }
        // Exchange insights
        if (suspiciousActivity.exchangeDeposits.detected) {
            insights.push('🤖 AI Insight: Potential exchange deposits detected - scammer may be attempting to convert to fiat currency.');
        }
        // Peel chain insights
        if (suspiciousActivity.peelChains.detected) {
            insights.push('🤖 AI Insight: Peel chain pattern identified - systematic fund extraction technique commonly used by sophisticated actors.');
        }
        // Address reuse insights
        if (patterns.addressReuse.addressReuseDetected) {
            insights.push('🤖 AI Insight: Address reuse detected - this may indicate poor operational security by the scammer or wallet clustering opportunities.');
        }
        // Volume-based insights
        if (transactions.length > 20) {
            insights.push('🤖 AI Insight: High transaction volume suggests this may be part of a larger operation affecting multiple victims.');
        }
        // Depth-based insights
        const maxDepth = Math.max(...transactions.map(tx => tx.depth), 0);
        if (maxDepth > 5) {
            insights.push('🤖 AI Insight: Deep transaction chains indicate sophisticated laundering techniques - professional investigation recommended.');
        }
        // Consolidation insights
        if (suspiciousActivity.consolidationPatterns.detected) {
            insights.push('🤖 AI Insight: Fund consolidation patterns suggest coordination between multiple addresses - possible criminal organization.');
        }
        return insights;
    }
    /**
     * Analyze transaction patterns for suspicious activity detection
     */
    analyzeTransactionPatterns(transactions) {
        if (transactions.length === 0) {
            return this.getEmptyPatternAnalysis();
        }
        logger_1.logger.info(`Analyzing patterns for ${transactions.length} transactions`);
        const timingAnalysis = this.analyzeTimingPatterns(transactions);
        const amountAnalysis = this.analyzeAmountPatterns(transactions);
        const addressReuse = this.analyzeAddressReuse(transactions);
        const clusteringHints = this.analyzeClusteringHints(transactions);
        const riskIndicators = this.calculateRiskIndicators(transactions);
        return {
            timingAnalysis,
            amountAnalysis,
            addressReuse,
            clusteringHints,
            riskIndicators,
        };
    }
    /**
     * Detect various types of suspicious activity patterns
     */
    detectSuspiciousActivity(transactions) {
        const mixingServices = this.detectMixingServices(transactions);
        const exchangeDeposits = this.detectExchangeDeposits(transactions);
        const peelChains = this.detectPeelChains(transactions);
        const consolidationPatterns = this.detectConsolidationPatterns(transactions);
        const privacyCoinInteractions = this.detectPrivacyCoinInteractions(transactions);
        // Calculate overall suspicion score
        let overallSuspicionScore = 0;
        const activities = { mixingServices, exchangeDeposits, peelChains, consolidationPatterns, privacyCoinInteractions };
        for (const [activityName, activity] of Object.entries(activities)) {
            if (activity.detected) {
                const weight = default_1.SUSPICIOUS_ACTIVITY_WEIGHTS[activityName] || 1;
                overallSuspicionScore += activity.severity * weight;
            }
        }
        const suspicionLevel = this.classifySuspicionLevel(overallSuspicionScore);
        return {
            mixingServices,
            exchangeDeposits,
            peelChains,
            consolidationPatterns,
            privacyCoinInteractions,
            overallSuspicionScore,
            suspicionLevel,
        };
    }
    /**
     * Generate comprehensive risk assessment
     */
    generateRiskAssessment(transactions, patterns, suspiciousActivity) {
        const baseRiskScore = patterns.riskIndicators.riskScore;
        const suspicionScore = suspiciousActivity.overallSuspicionScore;
        const compositeRiskScore = baseRiskScore + suspicionScore;
        const finalRiskLevel = this.classifyRiskLevel(compositeRiskScore);
        const riskFactors = [...patterns.riskIndicators.riskFactors];
        const suspiciousActivities = Object.entries(suspiciousActivity)
            .filter(([key, value]) => typeof value === 'object' &&
            value !== null &&
            'detected' in value &&
            value.detected)
            .map(([key]) => key);
        const recommendations = this.generateRiskRecommendations(finalRiskLevel, suspiciousActivity);
        return {
            compositeRiskScore,
            finalRiskLevel,
            baseRiskScore,
            suspicionScore,
            riskFactors,
            suspiciousActivities,
            recommendations,
        };
    }
    analyzeTimingPatterns(transactions) {
        if (transactions.length < 2) {
            return {
                averageIntervalSeconds: 0,
                rapidSuccessionCount: 0,
                totalIntervals: 0,
                suspiciousTiming: false,
                timePatterns: [],
            };
        }
        // Sort by timestamp
        const sortedTxs = transactions
            .filter(tx => tx.timestamp)
            .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        if (sortedTxs.length < 2) {
            return {
                averageIntervalSeconds: 0,
                rapidSuccessionCount: 0,
                totalIntervals: 0,
                suspiciousTiming: false,
                timePatterns: [],
            };
        }
        // Calculate intervals
        const intervals = [];
        for (let i = 1; i < sortedTxs.length; i++) {
            const interval = (new Date(sortedTxs[i].timestamp).getTime() -
                new Date(sortedTxs[i - 1].timestamp).getTime()) / 1000;
            intervals.push(interval);
        }
        const averageIntervalSeconds = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
        const rapidSuccessionCount = intervals.filter(interval => interval < 300).length; // < 5 minutes
        const suspiciousTiming = rapidSuccessionCount > intervals.length * 0.5;
        // Detect patterns
        const timePatterns = [];
        if (rapidSuccessionCount > 0) {
            timePatterns.push('rapid_succession');
        }
        if (averageIntervalSeconds < 60) {
            timePatterns.push('very_fast_transactions');
        }
        return {
            averageIntervalSeconds,
            rapidSuccessionCount,
            totalIntervals: intervals.length,
            suspiciousTiming,
            timePatterns,
        };
    }
    analyzeAmountPatterns(transactions) {
        if (transactions.length === 0) {
            return {
                totalAmount: 0,
                averageAmount: 0,
                maxAmount: 0,
                minAmount: 0,
                roundAmountsCount: 0,
                similarAmountsCount: 0,
                potentialStructuring: false,
                potentialSplitting: false,
            };
        }
        const amounts = transactions.map(tx => tx.amountBtc);
        const totalAmount = amounts.reduce((sum, amount) => sum + amount, 0);
        const averageAmount = totalAmount / amounts.length;
        const maxAmount = Math.max(...amounts);
        const minAmount = Math.min(...amounts);
        // Detect round amounts (potential structuring)
        const roundAmountsCount = amounts.filter(amount => amount === Math.round(amount * 100000000) / 100000000).length;
        // Detect similar amounts (potential splitting)
        let similarAmountsCount = 0;
        const threshold = 0.001; // 0.001 BTC threshold
        for (let i = 0; i < amounts.length; i++) {
            for (let j = i + 1; j < amounts.length; j++) {
                if (Math.abs(amounts[i] - amounts[j]) < threshold) {
                    similarAmountsCount++;
                }
            }
        }
        const potentialStructuring = roundAmountsCount > amounts.length * 0.3;
        const potentialSplitting = similarAmountsCount > 0;
        return {
            totalAmount,
            averageAmount,
            maxAmount,
            minAmount,
            roundAmountsCount,
            similarAmountsCount,
            potentialStructuring,
            potentialSplitting,
        };
    }
    analyzeAddressReuse(transactions) {
        const fromAddresses = transactions.map(tx => tx.fromAddress);
        const toAddresses = transactions.map(tx => tx.toAddress);
        const fromCounts = {};
        const toCounts = {};
        fromAddresses.forEach(addr => {
            fromCounts[addr] = (fromCounts[addr] || 0) + 1;
        });
        toAddresses.forEach(addr => {
            toCounts[addr] = (toCounts[addr] || 0) + 1;
        });
        const reusedFromAddresses = {};
        const reusedToAddresses = {};
        Object.entries(fromCounts).forEach(([addr, count]) => {
            if (count > 1) {
                reusedFromAddresses[addr] = count;
            }
        });
        Object.entries(toCounts).forEach(([addr, count]) => {
            if (count > 1) {
                reusedToAddresses[addr] = count;
            }
        });
        return {
            uniqueFromAddresses: Object.keys(fromCounts).length,
            uniqueToAddresses: Object.keys(toCounts).length,
            reusedFromAddresses,
            reusedToAddresses,
            addressReuseDetected: Object.keys(reusedFromAddresses).length > 0 || Object.keys(reusedToAddresses).length > 0,
        };
    }
    analyzeClusteringHints(transactions) {
        // Simple clustering based on timing and amounts
        const clusters = {};
        transactions.forEach(tx => {
            const clusterKey = `${tx.depth}_${Math.round(tx.amountBtc * 10000)}`;
            if (!clusters[clusterKey]) {
                clusters[clusterKey] = [];
            }
            clusters[clusterKey].push(tx);
        });
        const potentialClusters = Object.fromEntries(Object.entries(clusters).filter(([, txs]) => txs.length > 1));
        const clusterDetails = {};
        Object.entries(potentialClusters).forEach(([key, txs]) => {
            clusterDetails[key] = txs.length;
        });
        return {
            totalClusters: Object.keys(clusters).length,
            potentialClusters: Object.keys(potentialClusters).length,
            clusterDetails,
            clusteringDetected: Object.keys(potentialClusters).length > 0,
        };
    }
    calculateRiskIndicators(transactions) {
        let riskScore = 0;
        const riskFactors = [];
        // High transaction volume
        if (transactions.length > 10) {
            riskScore += 2;
            riskFactors.push('high_transaction_volume');
        }
        // Large amounts
        const totalAmount = transactions.reduce((sum, tx) => sum + tx.amountBtc, 0);
        if (totalAmount > 10) { // > 10 BTC
            riskScore += 3;
            riskFactors.push('large_amounts');
        }
        // Deep transaction chains
        const maxDepth = Math.max(...transactions.map(tx => tx.depth), 0);
        if (maxDepth > 3) {
            riskScore += 2;
            riskFactors.push('deep_transaction_chains');
        }
        // Many small transactions (potential mixing)
        const smallTransactions = transactions.filter(tx => tx.amountBtc < 0.01).length;
        if (smallTransactions > transactions.length * 0.6) {
            riskScore += 2;
            riskFactors.push('many_small_transactions');
        }
        const riskLevel = this.classifyRiskLevel(riskScore);
        return {
            riskScore,
            riskLevel,
            riskFactors,
            totalAmount,
            transactionCount: transactions.length,
            maxDepth,
        };
    }
    detectMixingServices(transactions) {
        let indicators = 0;
        const details = {};
        // Look for multiple small outputs (typical mixing pattern)
        const smallOutputs = transactions.filter(tx => tx.amountBtc < 0.01).length;
        details.smallOutputs = smallOutputs;
        if (smallOutputs > transactions.length * 0.6) {
            indicators++;
        }
        // Look for round amounts (mixing services often use round amounts)
        const roundAmounts = transactions.filter(tx => tx.amountBtc === Math.round(tx.amountBtc * 100000000) / 100000000).length;
        details.roundAmounts = roundAmounts;
        if (roundAmounts > transactions.length * 0.4) {
            indicators++;
        }
        return {
            detected: indicators >= 1,
            severity: 3,
            confidence: Math.min(indicators / 2, 1),
            details,
            description: indicators >= 1 ? 'Potential mixing service usage detected' : undefined,
        };
    }
    detectExchangeDeposits(transactions) {
        // Look for addresses that receive from multiple sources
        const toAddressSources = {};
        transactions.forEach(tx => {
            if (!toAddressSources[tx.toAddress]) {
                toAddressSources[tx.toAddress] = new Set();
            }
            toAddressSources[tx.toAddress].add(tx.fromAddress);
        });
        const potentialExchanges = {};
        Object.entries(toAddressSources).forEach(([address, sources]) => {
            if (sources.size > 2) {
                potentialExchanges[address] = sources.size;
            }
        });
        return {
            detected: Object.keys(potentialExchanges).length > 0,
            severity: 2,
            confidence: Object.keys(potentialExchanges).length > 0 ? 0.7 : 0,
            details: {
                potentialExchanges,
                addressesWithMultipleSources: Object.keys(potentialExchanges).length,
            },
            description: Object.keys(potentialExchanges).length > 0 ? 'Potential exchange deposits detected' : undefined,
        };
    }
    detectPeelChains(transactions) {
        if (transactions.length < 3) {
            return {
                detected: false,
                severity: 2,
                confidence: 0,
                details: { reason: 'insufficient_transactions' },
            };
        }
        // Sort by depth to analyze chain progression
        const sortedTxs = transactions.sort((a, b) => a.depth - b.depth);
        // Look for decreasing amounts pattern
        let decreasingPattern = 0;
        for (let i = 1; i < sortedTxs.length; i++) {
            if (sortedTxs[i].amountBtc < sortedTxs[i - 1].amountBtc) {
                decreasingPattern++;
            }
        }
        const isPeelChain = decreasingPattern > sortedTxs.length * 0.6;
        const patternStrength = decreasingPattern / sortedTxs.length;
        return {
            detected: isPeelChain,
            severity: 2,
            confidence: patternStrength,
            details: {
                decreasingTransactions: decreasingPattern,
                totalTransactions: sortedTxs.length,
                patternStrength,
            },
            description: isPeelChain ? 'Peel chain pattern detected' : undefined,
        };
    }
    detectConsolidationPatterns(transactions) {
        // Look for addresses that receive from multiple sources
        const consolidationAddresses = {};
        transactions.forEach(tx => {
            if (!consolidationAddresses[tx.toAddress]) {
                consolidationAddresses[tx.toAddress] = {
                    sources: new Set(),
                    totalAmount: 0,
                    transactionCount: 0,
                };
            }
            consolidationAddresses[tx.toAddress].sources.add(tx.fromAddress);
            consolidationAddresses[tx.toAddress].totalAmount += tx.amountBtc;
            consolidationAddresses[tx.toAddress].transactionCount++;
        });
        const significantConsolidation = {};
        Object.entries(consolidationAddresses).forEach(([address, data]) => {
            if (data.sources.size > 2 && data.totalAmount > 0.1) {
                significantConsolidation[address] = {
                    sourceCount: data.sources.size,
                    totalAmount: data.totalAmount,
                    transactionCount: data.transactionCount,
                };
            }
        });
        return {
            detected: Object.keys(significantConsolidation).length > 0,
            severity: 1,
            confidence: Object.keys(significantConsolidation).length > 0 ? 0.6 : 0,
            details: { consolidationAddresses: significantConsolidation },
            description: Object.keys(significantConsolidation).length > 0 ? 'Fund consolidation patterns detected' : undefined,
        };
    }
    detectPrivacyCoinInteractions(transactions) {
        // Placeholder for cross-chain analysis
        return {
            detected: false,
            severity: 3,
            confidence: 0,
            details: { note: 'Cross-chain analysis not yet implemented' },
        };
    }
    classifyRiskLevel(score) {
        if (score >= default_1.RISK_THRESHOLDS.CRITICAL)
            return 'CRITICAL';
        if (score >= default_1.RISK_THRESHOLDS.HIGH)
            return 'HIGH';
        if (score >= default_1.RISK_THRESHOLDS.MEDIUM)
            return 'MEDIUM';
        if (score >= default_1.RISK_THRESHOLDS.LOW)
            return 'LOW';
        return 'MINIMAL';
    }
    classifySuspicionLevel(score) {
        if (score >= 8)
            return 'VERY_HIGH';
        if (score >= 6)
            return 'HIGH';
        if (score >= 4)
            return 'MEDIUM';
        if (score >= 2)
            return 'LOW';
        return 'MINIMAL';
    }
    generateRiskRecommendations(riskLevel, suspiciousActivity) {
        const recommendations = [];
        if (riskLevel === 'CRITICAL' || riskLevel === 'HIGH') {
            recommendations.push('Immediate escalation to law enforcement recommended');
            recommendations.push('Consider freezing related accounts if possible');
            recommendations.push('Implement enhanced monitoring for related addresses');
        }
        if (riskLevel === 'HIGH' || riskLevel === 'MEDIUM') {
            recommendations.push('Enhanced due diligence required');
            recommendations.push('Consider filing suspicious activity report');
        }
        // Activity-specific recommendations
        if (suspiciousActivity.mixingServices.detected) {
            recommendations.push('Potential mixing service usage detected - investigate further');
        }
        if (suspiciousActivity.exchangeDeposits.detected) {
            recommendations.push('Potential exchange deposits identified - contact exchanges for cooperation');
        }
        if (suspiciousActivity.peelChains.detected) {
            recommendations.push('Peel chain pattern detected - funds may be systematically extracted');
        }
        return recommendations;
    }
    getEmptyPatternAnalysis() {
        return {
            timingAnalysis: {
                averageIntervalSeconds: 0,
                rapidSuccessionCount: 0,
                totalIntervals: 0,
                suspiciousTiming: false,
                timePatterns: [],
            },
            amountAnalysis: {
                totalAmount: 0,
                averageAmount: 0,
                maxAmount: 0,
                minAmount: 0,
                roundAmountsCount: 0,
                similarAmountsCount: 0,
                potentialStructuring: false,
                potentialSplitting: false,
            },
            addressReuse: {
                uniqueFromAddresses: 0,
                uniqueToAddresses: 0,
                reusedFromAddresses: {},
                reusedToAddresses: {},
                addressReuseDetected: false,
            },
            clusteringHints: {
                totalClusters: 0,
                potentialClusters: 0,
                clusterDetails: {},
                clusteringDetected: false,
            },
            riskIndicators: {
                riskScore: 0,
                riskLevel: 'MINIMAL',
                riskFactors: [],
                totalAmount: 0,
                transactionCount: 0,
                maxDepth: 0,
            },
        };
    }
}
exports.AnalysisService = AnalysisService;
//# sourceMappingURL=analysis.js.map