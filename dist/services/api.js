"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BitcoinAPIService = void 0;
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("../utils/logger");
class BitcoinAPIService {
    constructor(config) {
        this.requestCount = 0;
        this.lastRequestTime = 0;
        this.config = config;
        this.client = axios_1.default.create({
            baseURL: config.apiBaseUrl,
            timeout: config.requestTimeout,
            headers: {
                'User-Agent': 'Bitcoin-Forensics-Tool/3.0.0',
                'Accept': 'application/json',
            },
        });
        this.setupInterceptors();
    }
    setupInterceptors() {
        // Request interceptor for rate limiting
        this.client.interceptors.request.use(async (config) => {
            await this.enforceRateLimit();
            this.requestCount++;
            return config;
        });
        // Response interceptor for logging and error handling
        this.client.interceptors.response.use((response) => {
            (0, logger_1.logAPICall)(response.config.url || 'unknown', true, Date.now() - this.lastRequestTime);
            return response;
        }, (error) => {
            const duration = Date.now() - this.lastRequestTime;
            (0, logger_1.logAPICall)(error.config?.url || 'unknown', false, duration);
            if (error.response?.status === 429) {
                logger_1.logger.warn('Rate limit exceeded', {
                    url: error.config?.url,
                    retryAfter: error.response.headers['retry-after']
                });
            }
            return Promise.reject(error);
        });
    }
    async enforceRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        if (timeSinceLastRequest < this.config.rateLimitDelay) {
            const waitTime = this.config.rateLimitDelay - timeSinceLastRequest;
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        this.lastRequestTime = Date.now();
    }
    async getTransaction(txid) {
        try {
            const response = await this.retryRequest(() => this.client.get(`/tx/${txid}`));
            return {
                success: true,
                data: response.data,
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { txid, action: 'getTransaction' });
            if (error.response?.status === 404) {
                return {
                    success: false,
                    error: `Transaction not found: ${txid}`,
                };
            }
            if (error.response?.status === 429) {
                return {
                    success: false,
                    error: 'Rate limit exceeded',
                    rateLimited: true,
                    retryAfter: parseInt(error.response.headers['retry-after'] || '60'),
                };
            }
            return {
                success: false,
                error: `Failed to fetch transaction: ${error.message}`,
            };
        }
    }
    async getOutspend(txid, vout) {
        try {
            const response = await this.retryRequest(() => this.client.get(`/tx/${txid}/outspend/${vout}`));
            return {
                success: true,
                data: response.data,
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { txid, vout, action: 'getOutspend' });
            if (error.response?.status === 404) {
                return {
                    success: false,
                    error: `Outspend not found: ${txid}:${vout}`,
                };
            }
            return {
                success: false,
                error: `Failed to fetch outspend: ${error.message}`,
            };
        }
    }
    async getAddressInfo(address) {
        try {
            const response = await this.retryRequest(() => this.client.get(`/address/${address}`));
            return {
                success: true,
                data: response.data,
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { address, action: 'getAddressInfo' });
            return {
                success: false,
                error: `Failed to fetch address info: ${error.message}`,
            };
        }
    }
    async getAddressTransactions(address, lastSeenTxid) {
        try {
            const url = lastSeenTxid
                ? `/address/${address}/txs/chain/${lastSeenTxid}`
                : `/address/${address}/txs`;
            const response = await this.retryRequest(() => this.client.get(url));
            return {
                success: true,
                data: response.data,
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { address, lastSeenTxid, action: 'getAddressTransactions' });
            return {
                success: false,
                error: `Failed to fetch address transactions: ${error.message}`,
            };
        }
    }
    async retryRequest(requestFn) {
        let lastError;
        for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
            try {
                return await requestFn();
            }
            catch (error) {
                lastError = error;
                if (error.response?.status === 429) {
                    // Rate limited - wait longer
                    const retryAfter = parseInt(error.response.headers['retry-after'] || '60');
                    const waitTime = Math.min(retryAfter * 1000, 60000); // Max 60 seconds
                    logger_1.logger.warn(`Rate limited, waiting ${waitTime}ms before retry ${attempt}/${this.config.maxRetries}`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                    continue;
                }
                if (error.response?.status === 404) {
                    // Don't retry 404s
                    throw error;
                }
                if (attempt < this.config.maxRetries) {
                    // Exponential backoff for other errors
                    const waitTime = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // Max 10 seconds
                    logger_1.logger.warn(`Request failed, retrying in ${waitTime}ms (attempt ${attempt}/${this.config.maxRetries})`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                }
            }
        }
        throw lastError;
    }
    getRequestCount() {
        return this.requestCount;
    }
    resetRequestCount() {
        this.requestCount = 0;
    }
}
exports.BitcoinAPIService = BitcoinAPIService;
//# sourceMappingURL=api.js.map