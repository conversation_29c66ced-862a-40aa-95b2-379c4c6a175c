import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import {
  InvestigationConfig,
  TransactionInfo,
  AddressInfo,
  InvestigationResults,
  UserInput,
  EvidenceItem,
  AuditLogEntry,
  ChainOfCustodyEntry,
} from '../types';
import { BitcoinAPIService } from '../services/api';
import { AnalysisService } from '../services/analysis';
import { EnhancedEvidenceService } from '../services/evidence';
import { AdvancedBitcoinTrackingService } from '../services/advanced-tracking';
import { EnhancedRiskAssessmentService } from '../services/enhanced-risk-assessment';
import { VictimFriendlyReportingService } from '../services/victim-friendly-reporting';
import {
  validateBitcoinAddress,
  validateTransactionId,
  getBitcoinAddressType,
} from '../utils/validation';
import {
  logger,
  logInvestigationStart,
  logInvestigationEnd,
  logEvidenceCreated,
  logAuditEvent,
} from '../utils/logger';
import { DEFAULT_CONFIG } from '../config/default';

export class BitcoinForensicsInvestigator {
  private config: InvestigationConfig;
  private apiService: BitcoinAPIService;
  private analysisService: AnalysisService;
  private enhancedEvidenceService: EnhancedEvidenceService;
  private advancedTrackingService: AdvancedBitcoinTrackingService;
  private enhancedRiskService: EnhancedRiskAssessmentService;
  private victimReportingService: VictimFriendlyReportingService;
  private investigationId: string;
  private evidenceItems: EvidenceItem[] = [];
  private auditLog: AuditLogEntry[] = [];
  private userInput?: UserInput;

  constructor(config: Partial<InvestigationConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.apiService = new BitcoinAPIService(this.config);
    this.analysisService = new AnalysisService();
    this.investigationId = uuidv4();

    // Create output directory
    this.ensureOutputDirectory();

    // Initialize enhanced evidence service
    this.enhancedEvidenceService = new EnhancedEvidenceService(
      this.investigationId,
      this.config.outputDirectory
    );

    // Initialize advanced tracking service
    this.advancedTrackingService = new AdvancedBitcoinTrackingService(this.apiService);

    // Initialize enhanced risk assessment service
    this.enhancedRiskService = new EnhancedRiskAssessmentService();

    // Initialize victim-friendly reporting service
    this.victimReportingService = new VictimFriendlyReportingService(this.config.outputDirectory);

    // Log initialization
    this.logAuditEvent('investigation_initialized', {
      investigationId: this.investigationId,
      config: this.config,
    });

    logger.info(`Initialized Bitcoin Forensics Investigator with enhanced evidence tracking`, {
      investigationId: this.investigationId,
    });
  }

  private ensureOutputDirectory(): void {
    if (!fs.existsSync(this.config.outputDirectory)) {
      fs.mkdirSync(this.config.outputDirectory, { recursive: true });
    }
  }

  private logAuditEvent(
    action: string,
    details: Record<string, any>,
    userId: string = 'system'
  ): void {
    const auditEntry: AuditLogEntry = {
      timestamp: new Date().toISOString(),
      investigationId: this.investigationId,
      action,
      details,
      userId,
      ipAddress: 'localhost', // In a real system, this would be the actual IP
    };

    this.auditLog.push(auditEntry);
    logAuditEvent(this.investigationId, action, details);
  }

  private createEvidenceItem(
    evidenceType: string,
    description: string,
    data: Record<string, any>
  ): EvidenceItem {
    const evidenceId = uuidv4();

    // Create hash of the evidence data for integrity
    const evidenceHash = crypto
      .createHash('sha256')
      .update(JSON.stringify(data, null, 0))
      .digest('hex');

    // Initialize chain of custody
    const chainOfCustody: ChainOfCustodyEntry[] = [
      {
        timestamp: new Date().toISOString(),
        action: 'evidence_created',
        userId: 'system',
        description: `Evidence item created: ${description}`,
      },
    ];

    const evidenceItem: EvidenceItem = {
      evidenceId,
      investigationId: this.investigationId,
      evidenceType: evidenceType as any,
      description,
      data,
      timestamp: new Date().toISOString(),
      hashValue: evidenceHash,
      chainOfCustody,
    };

    this.evidenceItems.push(evidenceItem);
    logEvidenceCreated(evidenceId, evidenceType, description);

    this.logAuditEvent('evidence_created', {
      evidenceId,
      evidenceType,
      description,
      hash: evidenceHash,
    });

    return evidenceItem;
  }

  async startInvestigation(userInput: UserInput): Promise<InvestigationResults> {
    this.userInput = userInput;

    logInvestigationStart(this.investigationId, userInput);

    this.logAuditEvent('investigation_started', {
      initialTxid: userInput.initialTxid,
      targetAddress: userInput.targetAddress,
      maxDepth: userInput.maxDepth,
      victimName: userInput.victimName,
    });

    logger.info('🕵️  Starting Bitcoin forensic investigation', {
      investigationId: this.investigationId,
      txid: userInput.initialTxid,
      address: userInput.targetAddress,
      victimName: userInput.victimName,
    });

    try {
      // Validate inputs
      if (!validateTransactionId(userInput.initialTxid)) {
        throw new Error(`Invalid transaction ID: ${userInput.initialTxid}`);
      }

      if (!validateBitcoinAddress(userInput.targetAddress)) {
        throw new Error(`Invalid Bitcoin address: ${userInput.targetAddress}`);
      }

      // Perform transaction tracing
      const { transactions, addresses } = await this.traceTransactions(
        userInput.initialTxid,
        userInput.targetAddress,
        userInput.maxDepth
      );

      // Create basic results
      const basicResults = {
        transactionCount: transactions.length,
        addressCount: addresses.length,
        totalAmount: transactions.reduce((sum, tx) => sum + tx.amountBtc, 0),
      };

      // Create enhanced evidence for transactions and addresses
      transactions.forEach(tx => {
        const evidence = this.enhancedEvidenceService.createTransactionEvidence(tx);
        this.enhancedEvidenceService.updateChainOfCustody(
          evidence.evidenceId,
          'evidence_collected',
          `Transaction evidence collected during investigation`,
          'investigator'
        );
      });

      addresses.forEach(addr => {
        const evidence = this.enhancedEvidenceService.createAddressEvidence(addr);
        this.enhancedEvidenceService.updateChainOfCustody(
          evidence.evidenceId,
          'evidence_collected',
          `Address evidence collected during investigation`,
          'investigator'
        );
      });

      // Perform pattern analysis
      const patterns = this.analysisService.analyzeTransactionPatterns(transactions);
      const suspiciousActivity = this.analysisService.detectSuspiciousActivity(transactions);

      // Perform advanced tracking analysis
      const advancedTracking = await this.advancedTrackingService.performAdvancedTracking(
        transactions,
        addresses
      );

      // Perform enhanced risk assessment
      const enhancedRisk = await this.enhancedRiskService.performEnhancedRiskAssessment(
        transactions,
        addresses,
        patterns,
        suspiciousActivity
      );

      // Generate enhanced evidence package
      const evidencePackagePath = await this.enhancedEvidenceService.exportEvidencePackage();
      const evidenceStats = this.enhancedEvidenceService.getStatistics();
      const auditReport = this.enhancedEvidenceService.generateAuditReport();

      // Create investigation results structure
      const results: InvestigationResults = {
        investigationId: this.investigationId,
        timestamp: new Date().toISOString(),
        inputParameters: {
          initialTxid: userInput.initialTxid,
          targetAddress: userInput.targetAddress,
          maxDepth: userInput.maxDepth,
          victimName: userInput.victimName,
          caseDescription: userInput.caseDescription,
        },
        basicResults,
        detailedTransactions: transactions,
        addressAnalysis: addresses,
        advancedAnalysis: {
          transactionPatterns: patterns,
          suspiciousActivity: suspiciousActivity,
          riskAssessment: {
            compositeRiskScore: enhancedRisk.compositeRiskScore,
            finalRiskLevel: enhancedRisk.finalRiskLevel,
            baseRiskScore: enhancedRisk.baseRiskScore,
            suspicionScore: enhancedRisk.suspicionScore,
            riskFactors: enhancedRisk.riskFactorNames,
            suspiciousActivities: enhancedRisk.suspiciousActivities,
            recommendations: enhancedRisk.recommendations,
          },
        },
        investigationSummary: {
          status: 'completed',
          keyMetrics: {
            totalTransactions: transactions.length,
            totalAmountBtc: basicResults.totalAmount,
            uniqueAddresses: addresses.length,
            maximumDepth: Math.max(...transactions.map(tx => tx.depth), 0),
            riskLevel: 'UNKNOWN', // Will be determined by analysis
          },
          keyConcerns: [],
          investigationQuality: {
            qualityScore: 0,
            qualityLevel: 'MEDIUM',
            qualityFactors: [],
            completenessPercentage: 0,
          },
          nextSteps: [],
          timeline: [],
        },
        evidencePackage: this.evidenceItems,
        auditTrail: this.auditLog,
        enhancedEvidence: {
          totalItems: evidenceStats.totalEvidence,
          categories: Object.keys(evidenceStats.byCategory),
          integrityStatus: evidenceStats.integrityStatus,
          packagePath: evidencePackagePath,
          auditReport,
        },
        advancedTracking,
        enhancedRiskAssessment: enhancedRisk,
      };

      // Generate victim-friendly report
      const victimReportPath = await this.victimReportingService.generateVictimFriendlyReport(
        results,
        enhancedRisk,
        advancedTracking
      );

      // Add victim report path to results
      results.enhancedEvidence!.victimReportPath = victimReportPath;

      logInvestigationEnd(this.investigationId, basicResults);

      this.logAuditEvent('investigation_completed', {
        transactionCount: transactions.length,
        addressCount: addresses.length,
        totalAmount: basicResults.totalAmount,
      });

      return results;
    } catch (error: any) {
      logger.error('Investigation failed', {
        investigationId: this.investigationId,
        error: error.message,
      });

      this.logAuditEvent('investigation_failed', {
        error: error.message,
        stack: error.stack,
      });

      throw error;
    }
  }

  private async traceTransactions(
    initialTxid: string,
    targetAddress: string,
    maxDepth: number
  ): Promise<{ transactions: TransactionInfo[]; addresses: AddressInfo[] }> {
    const transactions: TransactionInfo[] = [];
    const addressMap = new Map<string, AddressInfo>();
    const visitedTxs = new Set<string>([initialTxid]);
    const queue: Array<{ txid: string; address: string; depth: number }> = [
      { txid: initialTxid, address: targetAddress, depth: 0 },
    ];

    // Add initial address to the map
    addressMap.set(targetAddress, {
      address: targetAddress,
      addressType: getBitcoinAddressType(targetAddress),
      totalReceived: 0,
      totalSent: 0,
      balance: 0,
      transactionCount: 0,
      firstSeen: new Date().toISOString(),
    });

    while (queue.length > 0) {
      const { txid: currentTxid, address: sourceAddress, depth } = queue.shift()!;

      if (depth >= maxDepth) {
        logger.info(
          `Reached maximum depth ${maxDepth} for address ${sourceAddress.substring(0, 8)}...`
        );
        continue;
      }

      logger.info(`Processing transaction ${currentTxid} at depth ${depth}`);

      // Get transaction data
      const txResponse = await this.apiService.getTransaction(currentTxid);
      if (!txResponse.success || !txResponse.data) {
        logger.warn(`Failed to fetch transaction ${currentTxid}: ${txResponse.error}`);
        continue;
      }

      const txData = txResponse.data;

      // Find outputs that go TO the source address
      const relevantOutputs = txData.vout
        .map((vout, index) => ({ vout, index }))
        .filter(({ vout }) => vout.scriptpubkey_address === sourceAddress);

      // For each relevant output, check if it gets spent
      for (const { vout, index } of relevantOutputs) {
        const outspendResponse = await this.apiService.getOutspend(currentTxid, index);
        if (!outspendResponse.success || !outspendResponse.data?.spent) {
          continue;
        }

        const outspendData = outspendResponse.data;
        const spendingTxid = outspendData.txid!;

        if (visitedTxs.has(spendingTxid)) {
          continue;
        }

        visitedTxs.add(spendingTxid);

        // Get the spending transaction
        const spendingTxResponse = await this.apiService.getTransaction(spendingTxid);
        if (!spendingTxResponse.success || !spendingTxResponse.data) {
          continue;
        }

        const spendingTxData = spendingTxResponse.data;

        // Analyze where the funds went
        for (const nextVout of spendingTxData.vout) {
          const newAddress = nextVout.scriptpubkey_address;
          if (!newAddress || newAddress === sourceAddress) {
            continue;
          }

          const amountBtc = nextVout.value / 100_000_000;

          // Create transaction info
          const transactionInfo: TransactionInfo = {
            txid: spendingTxid,
            fromAddress: sourceAddress,
            toAddress: newAddress,
            amountBtc,
            depth: depth + 1,
            timestamp: new Date().toISOString(),
            blockHeight: spendingTxData.status.block_height,
            confirmations: spendingTxData.status.confirmed,
            investigationId: this.investigationId,
            fees: spendingTxData.fee / 100_000_000,
            inputCount: spendingTxData.vin.length,
            outputCount: spendingTxData.vout.length,
          };

          transactions.push(transactionInfo);

          // Update address info
          if (!addressMap.has(newAddress)) {
            addressMap.set(newAddress, {
              address: newAddress,
              addressType: getBitcoinAddressType(newAddress),
              totalReceived: 0,
              totalSent: 0,
              balance: 0,
              transactionCount: 0,
              firstSeen: new Date().toISOString(),
            });
          }

          const addressInfo = addressMap.get(newAddress)!;
          addressInfo.totalReceived += amountBtc;
          addressInfo.transactionCount++;
          addressInfo.lastSeen = new Date().toISOString();

          // Create evidence item
          this.createEvidenceItem(
            'transaction',
            `Transaction ${spendingTxid}: ${amountBtc.toFixed(8)} BTC from ${sourceAddress.substring(0, 8)}... to ${newAddress.substring(0, 8)}...`,
            {
              transactionInfo,
              rawTransactionData: spendingTxData,
              discoveryContext: {
                parentTxid: currentTxid,
                outputIndex: index,
                discoveryDepth: depth,
              },
            }
          );

          // Continue tracing
          queue.push({ txid: spendingTxid, address: newAddress, depth: depth + 1 });
        }
      }
    }

    return {
      transactions,
      addresses: Array.from(addressMap.values()),
    };
  }

  getInvestigationId(): string {
    return this.investigationId;
  }

  getEvidenceItems(): EvidenceItem[] {
    return [...this.evidenceItems];
  }

  getAuditLog(): AuditLogEntry[] {
    return [...this.auditLog];
  }
}
