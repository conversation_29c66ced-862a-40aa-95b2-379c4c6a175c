/**
 * Core type definitions for Bitcoin Forensic Investigation Tool
 */

export interface InvestigationConfig {
  apiBaseUrl: string;
  rateLimitDelay: number;
  maxRetries: number;
  requestTimeout: number;
  maxDepth: number;
  saveReports: boolean;
  saveVisualizations: boolean;
  outputDirectory: string;
  enableAIInsights: boolean;
  verboseLogging: boolean;
}

export interface TransactionInfo {
  txid: string;
  fromAddress: string;
  toAddress: string;
  amountBtc: number;
  depth: number;
  timestamp: string;
  blockHeight?: number;
  confirmations: boolean;
  investigationId: string;
  fees?: number;
  inputCount?: number;
  outputCount?: number;
}

export interface AddressInfo {
  address: string;
  addressType: 'legacy' | 'segwit' | 'taproot' | 'unknown';
  totalReceived: number;
  totalSent: number;
  balance: number;
  transactionCount: number;
  firstSeen?: string;
  lastSeen?: string;
  riskScore?: number;
  tags?: string[];
}

export interface AuditLogEntry {
  timestamp: string;
  investigationId: string;
  action: string;
  details: Record<string, any>;
  userId: string;
  ipAddress: string;
}

export interface EvidenceItem {
  evidenceId: string;
  investigationId: string;
  evidenceType: 'transaction' | 'address' | 'pattern' | 'analysis' | 'summary';
  description: string;
  data: Record<string, any>;
  timestamp: string;
  hashValue: string;
  chainOfCustody: ChainOfCustodyEntry[];
}

export interface ChainOfCustodyEntry {
  timestamp: string;
  action: string;
  userId: string;
  description: string;
  signature?: string;
}

export interface RiskAssessment {
  compositeRiskScore: number;
  finalRiskLevel: 'MINIMAL' | 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  baseRiskScore: number;
  suspicionScore: number;
  riskFactors: string[];
  suspiciousActivities: string[];
  recommendations: string[];
}

export interface PatternAnalysis {
  timingAnalysis: TimingAnalysis;
  amountAnalysis: AmountAnalysis;
  addressReuse: AddressReuseAnalysis;
  clusteringHints: ClusteringAnalysis;
  riskIndicators: RiskIndicators;
}

export interface TimingAnalysis {
  averageIntervalSeconds: number;
  rapidSuccessionCount: number;
  totalIntervals: number;
  suspiciousTiming: boolean;
  timePatterns: string[];
}

export interface AmountAnalysis {
  totalAmount: number;
  averageAmount: number;
  maxAmount: number;
  minAmount: number;
  roundAmountsCount: number;
  similarAmountsCount: number;
  potentialStructuring: boolean;
  potentialSplitting: boolean;
}

export interface AddressReuseAnalysis {
  uniqueFromAddresses: number;
  uniqueToAddresses: number;
  reusedFromAddresses: Record<string, number>;
  reusedToAddresses: Record<string, number>;
  addressReuseDetected: boolean;
}

export interface ClusteringAnalysis {
  totalClusters: number;
  potentialClusters: number;
  clusterDetails: Record<string, number>;
  clusteringDetected: boolean;
}

export interface RiskIndicators {
  riskScore: number;
  riskLevel: string;
  riskFactors: string[];
  totalAmount: number;
  transactionCount: number;
  maxDepth: number;
}

export interface SuspiciousActivity {
  mixingServices: SuspiciousActivityDetail;
  exchangeDeposits: SuspiciousActivityDetail;
  peelChains: SuspiciousActivityDetail;
  consolidationPatterns: SuspiciousActivityDetail;
  privacyCoinInteractions: SuspiciousActivityDetail;
  overallSuspicionScore: number;
  suspicionLevel: string;
}

export interface SuspiciousActivityDetail {
  detected: boolean;
  severity: number;
  confidence: number;
  details: Record<string, any>;
  description?: string;
}

export interface InvestigationSummary {
  status: string;
  keyMetrics: {
    totalTransactions: number;
    totalAmountBtc: number;
    uniqueAddresses: number;
    maximumDepth: number;
    riskLevel: string;
  };
  keyConcerns: string[];
  investigationQuality: QualityAssessment;
  nextSteps: string[];
  timeline: TimelineEntry[];
}

export interface QualityAssessment {
  qualityScore: number;
  qualityLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  qualityFactors: string[];
  completenessPercentage: number;
}

export interface TimelineEntry {
  timestamp: string;
  event: string;
  description: string;
  significance: 'low' | 'medium' | 'high';
}

export interface InvestigationResults {
  investigationId: string;
  timestamp: string;
  inputParameters: {
    initialTxid: string;
    targetAddress: string;
    maxDepth: number;
    victimName?: string;
    caseDescription?: string;
  };
  basicResults: {
    transactionCount: number;
    addressCount: number;
    totalAmount: number;
  };
  detailedTransactions: TransactionInfo[];
  addressAnalysis: AddressInfo[];
  advancedAnalysis: {
    transactionPatterns: PatternAnalysis;
    suspiciousActivity: SuspiciousActivity;
    riskAssessment: RiskAssessment;
  };
  investigationSummary: InvestigationSummary;
  evidencePackage: EvidenceItem[];
  auditTrail: AuditLogEntry[];
  // Enhanced evidence tracking
  enhancedEvidence?: {
    totalItems: number;
    categories: string[];
    integrityStatus: { valid: number; invalid: number };
    packagePath?: string;
    auditReport?: any;
    victimReportPath?: string;
  };
  // Advanced tracking results
  advancedTracking?: any;
  // Enhanced risk assessment
  enhancedRiskAssessment?: any;
}

export interface UserInput {
  victimName: string;
  caseDescription: string;
  initialTxid: string;
  targetAddress: string;
  scamAmount: number;
  maxDepth: number;
  contactInfo?: string;
  incidentDate?: string;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  rateLimited?: boolean;
  retryAfter?: number;
}

export interface BlockstreamTransaction {
  txid: string;
  version: number;
  locktime: number;
  vin: Array<{
    txid: string;
    vout: number;
    prevout: {
      scriptpubkey: string;
      scriptpubkey_asm: string;
      scriptpubkey_type: string;
      scriptpubkey_address?: string;
      value: number;
    };
    scriptsig: string;
    scriptsig_asm: string;
    witness?: string[];
    is_coinbase: boolean;
    sequence: number;
  }>;
  vout: Array<{
    scriptpubkey: string;
    scriptpubkey_asm: string;
    scriptpubkey_type: string;
    scriptpubkey_address?: string;
    value: number;
  }>;
  size: number;
  weight: number;
  fee: number;
  status: {
    confirmed: boolean;
    block_height?: number;
    block_hash?: string;
    block_time?: number;
  };
}

export interface BlockstreamOutspend {
  spent: boolean;
  txid?: string;
  vin?: number;
  status?: {
    confirmed: boolean;
    block_height?: number;
    block_hash?: string;
    block_time?: number;
  };
}
