import fs from 'fs';
import path from 'path';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import { format } from 'date-fns';
import { InvestigationResults, TransactionInfo, EvidenceItem } from '../types';
import { logger } from '../utils/logger';

export class ReportingService {
  private outputDirectory: string;

  constructor(outputDirectory: string = 'investigation_results') {
    this.outputDirectory = outputDirectory;

    // Ensure output directory exists
    if (!fs.existsSync(outputDirectory)) {
      fs.mkdirSync(outputDirectory, { recursive: true });
    }
  }

  /**
   * Generate comprehensive PDF report
   */
  async generatePDFReport(results: InvestigationResults): Promise<string> {
    try {
      logger.info('Generating PDF report', { investigationId: results.investigationId });

      const pdfDoc = await PDFDocument.create();
      const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
      const helveticaBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

      // Cover page
      await this.addCoverPage(pdfDoc, results, helveticaBold, helveticaFont);

      // Executive summary
      await this.addExecutiveSummary(pdfDoc, results, helveticaBold, helveticaFont);

      // Investigation details
      await this.addInvestigationDetails(pdfDoc, results, helveticaBold, helveticaFont);

      // Transaction analysis
      await this.addTransactionAnalysis(pdfDoc, results, helveticaBold, helveticaFont);

      // Risk assessment
      await this.addRiskAssessment(pdfDoc, results, helveticaBold, helveticaFont);

      // Evidence package
      await this.addEvidencePackage(pdfDoc, results, helveticaBold, helveticaFont);

      // Appendices
      await this.addAppendices(pdfDoc, results, helveticaBold, helveticaFont);

      // Save PDF
      const pdfBytes = await pdfDoc.save();
      const filename = path.join(
        this.outputDirectory,
        `investigation_report_${results.investigationId}_${format(new Date(), 'yyyyMMdd_HHmmss')}.pdf`
      );

      fs.writeFileSync(filename, pdfBytes);
      logger.info('PDF report generated', { filename });

      return filename;
    } catch (error: any) {
      logger.error('Error generating PDF report', { error: error.message });
      throw new Error(`Failed to generate PDF report: ${error.message}`);
    }
  }

  /**
   * Generate interactive HTML report
   */
  async generateHTMLReport(results: InvestigationResults): Promise<string> {
    try {
      logger.info('Generating HTML report', { investigationId: results.investigationId });

      const html = this.generateHTMLTemplate(results);

      const filename = path.join(
        this.outputDirectory,
        `investigation_report_${results.investigationId}_${format(new Date(), 'yyyyMMdd_HHmmss')}.html`
      );

      fs.writeFileSync(filename, html);
      logger.info('HTML report generated', { filename });

      return filename;
    } catch (error: any) {
      logger.error('Error generating HTML report', { error: error.message });
      throw new Error(`Failed to generate HTML report: ${error.message}`);
    }
  }

  /**
   * Generate JSON evidence package
   */
  async generateJSONEvidence(results: InvestigationResults): Promise<string> {
    try {
      logger.info('Generating JSON evidence package', { investigationId: results.investigationId });

      const evidencePackage = {
        metadata: {
          investigationId: results.investigationId,
          generatedAt: new Date().toISOString(),
          toolVersion: '3.0.0',
          format: 'json',
        },
        investigation: results,
        integrity: {
          totalTransactions: results.detailedTransactions.length,
          totalEvidence: results.evidencePackage.length,
          checksum: this.calculateChecksum(results),
        },
      };

      const filename = path.join(
        this.outputDirectory,
        `evidence_package_${results.investigationId}.json`
      );

      fs.writeFileSync(filename, JSON.stringify(evidencePackage, null, 2));
      logger.info('JSON evidence package generated', { filename });

      return filename;
    } catch (error: any) {
      logger.error('Error generating JSON evidence', { error: error.message });
      throw new Error(`Failed to generate JSON evidence: ${error.message}`);
    }
  }

  /**
   * Generate CSV data export
   */
  async generateCSVExport(results: InvestigationResults): Promise<string> {
    try {
      logger.info('Generating CSV export', { investigationId: results.investigationId });

      const csvLines = [
        'Transaction ID,From Address,To Address,Amount (BTC),Depth,Timestamp,Block Height,Confirmations,Fees (BTC)',
      ];

      results.detailedTransactions.forEach(tx => {
        csvLines.push(
          [
            tx.txid,
            tx.fromAddress,
            tx.toAddress,
            tx.amountBtc.toFixed(8),
            tx.depth.toString(),
            tx.timestamp,
            tx.blockHeight?.toString() || '',
            tx.confirmations.toString(),
            tx.fees?.toFixed(8) || '',
          ].join(',')
        );
      });

      const csvContent = csvLines.join('\n');
      const filename = path.join(
        this.outputDirectory,
        `transaction_data_${results.investigationId}.csv`
      );

      fs.writeFileSync(filename, csvContent);
      logger.info('CSV export generated', { filename });

      return filename;
    } catch (error: any) {
      logger.error('Error generating CSV export', { error: error.message });
      throw new Error(`Failed to generate CSV export: ${error.message}`);
    }
  }

  /**
   * Generate text report
   */
  async generateTextReport(results: InvestigationResults): Promise<string> {
    try {
      logger.info('Generating text report', { investigationId: results.investigationId });

      const lines = [
        '='.repeat(80),
        '                    BITCOIN FORENSIC INVESTIGATION REPORT',
        `                        Generated: ${format(new Date(), 'yyyy-MM-dd HH:mm:ss')}`,
        `                     Investigation ID: ${results.investigationId}`,
        '='.repeat(80),
        '',
        '📋 CASE INFORMATION',
        '-'.repeat(40),
        `Victim Name: ${results.inputParameters.victimName || 'N/A'}`,
        `Case Description: ${results.inputParameters.caseDescription || 'N/A'}`,
        `Investigation Date: ${format(new Date(results.timestamp), 'yyyy-MM-dd HH:mm:ss')}`,
        '',
        '🔍 INVESTIGATION PARAMETERS',
        '-'.repeat(40),
        `Initial Transaction ID: ${results.inputParameters.initialTxid}`,
        `Target Address: ${results.inputParameters.targetAddress}`,
        `Maximum Depth: ${results.inputParameters.maxDepth}`,
        '',
        '📊 INVESTIGATION SUMMARY',
        '-'.repeat(40),
        `Total Transactions Found: ${results.basicResults.transactionCount}`,
        `Total Addresses Discovered: ${results.basicResults.addressCount}`,
        `Total Amount Traced: ${results.basicResults.totalAmount.toFixed(8)} BTC`,
        `Risk Level: ${results.investigationSummary.keyMetrics.riskLevel}`,
        '',
      ];

      if (results.detailedTransactions.length > 0) {
        lines.push('📋 DETAILED TRANSACTION TRAIL');
        lines.push('-'.repeat(40));

        results.detailedTransactions.forEach((tx, index) => {
          lines.push(`Transaction #${index + 1} (Depth: ${tx.depth})`);
          lines.push(`   ➡️  From: ${tx.fromAddress}`);
          lines.push(`   🎯  To:   ${tx.toAddress}`);
          lines.push(`   💰  Amount: ${tx.amountBtc.toFixed(8)} BTC`);
          lines.push(`   🔗  TXID: ${tx.txid}`);
          if (tx.blockHeight) {
            lines.push(`   📦  Block: ${tx.blockHeight}`);
          }
          lines.push(`   ✅  Confirmed: ${tx.confirmations}`);
          lines.push(`   🕐  Timestamp: ${tx.timestamp}`);
          lines.push('');
        });
      }

      // Risk assessment
      if (results.advancedAnalysis.riskAssessment) {
        lines.push('🚨 RISK ASSESSMENT');
        lines.push('-'.repeat(40));
        lines.push(`Risk Level: ${results.advancedAnalysis.riskAssessment.finalRiskLevel}`);
        lines.push(`Risk Score: ${results.advancedAnalysis.riskAssessment.compositeRiskScore}/20`);

        if (results.advancedAnalysis.riskAssessment.recommendations.length > 0) {
          lines.push('');
          lines.push('💡 RECOMMENDATIONS:');
          results.advancedAnalysis.riskAssessment.recommendations.forEach(rec => {
            lines.push(`   • ${rec}`);
          });
        }
        lines.push('');
      }

      lines.push('='.repeat(80));
      lines.push('                           END OF REPORT');
      lines.push('='.repeat(80));

      const textContent = lines.join('\n');
      const filename = path.join(
        this.outputDirectory,
        `investigation_report_${results.investigationId}_${format(new Date(), 'yyyyMMdd_HHmmss')}.txt`
      );

      fs.writeFileSync(filename, textContent);
      logger.info('Text report generated', { filename });

      return filename;
    } catch (error: any) {
      logger.error('Error generating text report', { error: error.message });
      throw new Error(`Failed to generate text report: ${error.message}`);
    }
  }

  private async addCoverPage(
    pdfDoc: any,
    results: InvestigationResults,
    boldFont: any,
    regularFont: any
  ): Promise<void> {
    const page = pdfDoc.addPage([612, 792]); // Letter size
    const { width, height } = page.getSize();

    // Title
    page.drawText('BITCOIN FORENSIC INVESTIGATION REPORT', {
      x: 50,
      y: height - 100,
      size: 20,
      font: boldFont,
      color: rgb(0, 0, 0),
    });

    // Investigation ID
    page.drawText(`Investigation ID: ${results.investigationId}`, {
      x: 50,
      y: height - 140,
      size: 12,
      font: regularFont,
      color: rgb(0, 0, 0),
    });

    // Date
    page.drawText(`Generated: ${format(new Date(), 'yyyy-MM-dd HH:mm:ss')}`, {
      x: 50,
      y: height - 160,
      size: 12,
      font: regularFont,
      color: rgb(0, 0, 0),
    });

    // Victim information
    if (results.inputParameters.victimName) {
      page.drawText(`Victim: ${results.inputParameters.victimName}`, {
        x: 50,
        y: height - 200,
        size: 12,
        font: regularFont,
        color: rgb(0, 0, 0),
      });
    }

    // Warning notice
    page.drawText('CONFIDENTIAL - FOR AUTHORIZED INVESTIGATION PURPOSES ONLY', {
      x: 50,
      y: 50,
      size: 10,
      font: boldFont,
      color: rgb(0.8, 0, 0),
    });
  }

  private async addExecutiveSummary(
    pdfDoc: any,
    results: InvestigationResults,
    boldFont: any,
    regularFont: any
  ): Promise<void> {
    const page = pdfDoc.addPage([612, 792]);
    const { width, height } = page.getSize();
    let yPosition = height - 50;

    // Title
    page.drawText('EXECUTIVE SUMMARY', {
      x: 50,
      y: yPosition,
      size: 16,
      font: boldFont,
      color: rgb(0, 0, 0),
    });

    yPosition -= 40;

    // Key metrics
    const metrics = [
      `Transactions Traced: ${results.basicResults.transactionCount}`,
      `Addresses Discovered: ${results.basicResults.addressCount}`,
      `Total Amount: ${results.basicResults.totalAmount.toFixed(8)} BTC`,
      `Risk Level: ${results.investigationSummary.keyMetrics.riskLevel}`,
    ];

    metrics.forEach(metric => {
      page.drawText(metric, {
        x: 50,
        y: yPosition,
        size: 12,
        font: regularFont,
        color: rgb(0, 0, 0),
      });
      yPosition -= 20;
    });
  }

  private async addInvestigationDetails(
    pdfDoc: any,
    results: InvestigationResults,
    boldFont: any,
    regularFont: any
  ): Promise<void> {
    // Implementation for investigation details section
    // This would include parameters, methodology, etc.
  }

  private async addTransactionAnalysis(
    pdfDoc: any,
    results: InvestigationResults,
    boldFont: any,
    regularFont: any
  ): Promise<void> {
    // Implementation for transaction analysis section
    // This would include detailed transaction breakdown
  }

  private async addRiskAssessment(
    pdfDoc: any,
    results: InvestigationResults,
    boldFont: any,
    regularFont: any
  ): Promise<void> {
    // Implementation for risk assessment section
  }

  private async addEvidencePackage(
    pdfDoc: any,
    results: InvestigationResults,
    boldFont: any,
    regularFont: any
  ): Promise<void> {
    // Implementation for evidence package section
  }

  private async addAppendices(
    pdfDoc: any,
    results: InvestigationResults,
    boldFont: any,
    regularFont: any
  ): Promise<void> {
    // Implementation for appendices
  }

  private generateHTMLTemplate(results: InvestigationResults): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Forensic Investigation Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .chart { text-align: center; margin: 20px 0; }
        .chart img { max-width: 100%; height: auto; }
        .transaction { border: 1px solid #ddd; padding: 10px; margin: 5px 0; }
        .risk-high { color: #d32f2f; }
        .risk-medium { color: #f57c00; }
        .risk-low { color: #388e3c; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Bitcoin Forensic Investigation Report</h1>
        <p><strong>Investigation ID:</strong> ${results.investigationId}</p>
        <p><strong>Generated:</strong> ${format(new Date(), 'yyyy-MM-dd HH:mm:ss')}</p>
        ${results.inputParameters.victimName ? `<p><strong>Victim:</strong> ${results.inputParameters.victimName}</p>` : ''}
    </div>

    <div class="section">
        <h2>Executive Summary</h2>
        <ul>
            <li>Transactions Traced: ${results.basicResults.transactionCount}</li>
            <li>Addresses Discovered: ${results.basicResults.addressCount}</li>
            <li>Total Amount: ${results.basicResults.totalAmount.toFixed(8)} BTC</li>
            <li>Risk Level: <span class="risk-${results.investigationSummary.keyMetrics.riskLevel.toLowerCase()}">${results.investigationSummary.keyMetrics.riskLevel}</span></li>
        </ul>
    </div>

    <div class="section">
        <h2>Transaction Analysis</h2>
        <p>Visual charts will be available in future versions. For now, please refer to the detailed transaction list below.</p>
    </div>

    <div class="section">
        <h2>Detailed Transactions</h2>
        ${results.detailedTransactions
          .map(
            (tx, index) => `
            <div class="transaction">
                <h4>Transaction #${index + 1} (Depth: ${tx.depth})</h4>
                <p><strong>From:</strong> ${tx.fromAddress}</p>
                <p><strong>To:</strong> ${tx.toAddress}</p>
                <p><strong>Amount:</strong> ${tx.amountBtc.toFixed(8)} BTC</p>
                <p><strong>TXID:</strong> ${tx.txid}</p>
                <p><strong>Timestamp:</strong> ${tx.timestamp}</p>
            </div>
        `
          )
          .join('')}
    </div>

    <div class="section">
        <h2>Legal Notice</h2>
        <p><em>This report is generated for legitimate investigation purposes only.
        All information should be verified independently and used in accordance with applicable laws.</em></p>
    </div>
</body>
</html>`;
  }

  private calculateChecksum(results: InvestigationResults): string {
    const crypto = require('crypto');
    const data = JSON.stringify(results, null, 0);
    return crypto.createHash('sha256').update(data).digest('hex');
  }
}
